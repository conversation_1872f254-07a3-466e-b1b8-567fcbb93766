// Game constants
const BOARD_WIDTH = 7;
const BOARD_HEIGHT = 8;
const PIECE_TYPES = {
    PAWN: { name: '卒', value: 10, type: 'PAWN' },
    ADVISOR: { name: '士', value: 10, type: 'ADVISOR' },
    KNIGHT: { name: '马', value: 15, type: 'KNIGHT' },
    ELEPHANT: { name: '象', value: 15, type: 'ELEPHANT' },
    ROOK: { name: '车', value: 30, type: 'ROOK' },
    CANNON: { name: '炮', value: 30, type: 'CANNON' },
    GENERAL: { name: '将', value: 100, type: 'GENERAL' }
};

// Game state
let gameBoard = [];
let playerPiece = null;
let enemyPieces = [];
let score = 0;
let totalScore = parseInt(localStorage.getItem('chessTotalScore')) || 0;
let highScore = localStorage.getItem('chessGameHighScore') || 0;
let round = 1;
let gameOver = false;

// Assist mode
let assistMode = false;

// Piece upgrades - stored in localStorage
let pieceUpgrades = JSON.parse(localStorage.getItem('chessUpgrades')) || {
    PAWN: 0,
    ADVISOR: 0,
    KNIGHT: 0,
    ELEPHANT: 0,
    ROOK: 0,
    CANNON: 0,
    GENERAL: 0
};

// Track last move for highlighting
let lastMoveFrom = null;
let lastMoveTo = null;

// DOM elements
const gameBoardElement = document.getElementById('game-board');
const scoreElement = document.getElementById('score');
const totalScoreElement = document.getElementById('total-score');
const highScoreElement = document.getElementById('high-score');
const roundElement = document.getElementById('round');
const startButton = document.getElementById('start-button');
const shopButton = document.getElementById('shop-button');
const assistButton = document.getElementById('assist-button');
const shopElement = document.getElementById('shop');
const gameOverElement = document.getElementById('game-over');
const finalScoreElement = document.getElementById('final-score');
const messageElement = document.getElementById('message');
const restartButton = document.getElementById('restart-button');

// Get upgraded piece value
function getUpgradedValue(pieceType) {
    const baseValue = PIECE_TYPES[pieceType].value;
    const upgradeLevel = pieceUpgrades[pieceType];
    return Math.floor(baseValue + (baseValue * 0.5 * upgradeLevel));
}

// Get upgrade cost
function getUpgradeCost(pieceType) {
    const currentValue = getUpgradedValue(pieceType);
    return currentValue * 10;
}

// Update existing pieces on the board with new upgraded values
function updateExistingPieces(pieceType) {
    const newValue = getUpgradedValue(pieceType);
    
    // Update enemy pieces on the board
    enemyPieces.forEach(piece => {
        if (piece.type.type === pieceType) {
            piece.type.value = newValue;
        }
    });
    
    // Update pieces in the gameBoard array
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        for (let x = 0; x < BOARD_WIDTH; x++) {
            const piece = gameBoard[y][x];
            if (piece && !piece.isPlayer && piece.type.type === pieceType) {
                piece.type.value = newValue;
            }
        }
    }
    
    console.log(`Updated all ${pieceType} pieces to value ${newValue}`);
}

// Toggle assist mode
function toggleAssistMode() {
    assistMode = !assistMode;
    assistButton.textContent = assistMode ? '辅助: 开' : '辅助: 关';
    assistButton.classList.toggle('active', assistMode);

    // Show/hide assist panel
    const assistPanel = document.getElementById('assist-panel');
    if (assistMode) {
        assistPanel.classList.remove('hidden');
    } else {
        assistPanel.classList.add('hidden');
    }

    renderBoard();
}

// Update assist panel statistics
function updateAssistStats(playerMoves, safeMoves, bestMoves, enemyAttackZones) {
    if (!assistMode) return;

    const totalMovesElement = document.getElementById('total-moves');
    const safeMovesCountElement = document.getElementById('safe-moves-count');
    const bestMovesCountElement = document.getElementById('best-moves-count');
    const threatLevelElement = document.getElementById('threat-level');

    if (totalMovesElement) totalMovesElement.textContent = playerMoves.length;
    if (safeMovesCountElement) safeMovesCountElement.textContent = safeMoves.length;
    if (bestMovesCountElement) bestMovesCountElement.textContent = bestMoves.length;

    // Calculate threat level
    let threatLevel = 'low';
    let threatText = '低';

    if (enemyAttackZones.length > 0) {
        // Check if player's current position is under attack
        let playerUnderAttack = enemyAttackZones.some(zone =>
            zone.x === playerPiece.x && zone.y === playerPiece.y
        );

        // Also check for cannon threats specifically
        if (!playerUnderAttack) {
            playerUnderAttack = enemyPieces.some(enemyPiece => {
                if (enemyPiece.type.type === 'CANNON') {
                    return canCannonAttackPosition(enemyPiece, playerPiece.x, playerPiece.y);
                }
                return false;
            });
        }

        if (playerUnderAttack) {
            threatLevel = 'high';
            threatText = '高';
        } else {
            const safeRatio = safeMoves.length / Math.max(playerMoves.length, 1);
            if (safeRatio < 0.3) {
                threatLevel = 'high';
                threatText = '高';
            } else if (safeRatio < 0.6) {
                threatLevel = 'medium';
                threatText = '中';
            }
        }
    }

    if (threatLevelElement) {
        threatLevelElement.textContent = threatText;
        threatLevelElement.className = threatLevel;
    }
}

// Check if cannon can attack a specific position
function canCannonAttackPosition(cannonPiece, targetX, targetY) {
    // Check horizontal line
    if (cannonPiece.y === targetY) {
        const minX = Math.min(cannonPiece.x, targetX);
        const maxX = Math.max(cannonPiece.x, targetX);
        let piecesBetween = 0;

        for (let x = minX + 1; x < maxX; x++) {
            if (gameBoard[cannonPiece.y][x]) {
                piecesBetween++;
            }
        }

        // Cannon can attack if there's exactly 1 piece between (for capture),
        // or 0 pieces (for moving to empty space)
        const targetPiece = gameBoard[targetY][targetX];
        if (piecesBetween === 1 && targetPiece) {
            // Can capture with jump
            return true;
        } else if (piecesBetween === 0 && !targetPiece) {
            // Can move to empty space
            return true;
        }
        return false;
    }

    // Check vertical line
    if (cannonPiece.x === targetX) {
        const minY = Math.min(cannonPiece.y, targetY);
        const maxY = Math.max(cannonPiece.y, targetY);
        let piecesBetween = 0;

        for (let y = minY + 1; y < maxY; y++) {
            if (gameBoard[y][cannonPiece.x]) {
                piecesBetween++;
            }
        }

        // Cannon can attack if there's exactly 1 piece between (for capture),
        // or 0 pieces (for moving to empty space)
        const targetPiece = gameBoard[targetY][targetX];
        if (piecesBetween === 1 && targetPiece) {
            // Can capture with jump
            return true;
        } else if (piecesBetween === 0 && !targetPiece) {
            // Can move to empty space
            return true;
        }
        return false;
    }

    return false;
}

// Get enemy attack zones (different from movement for cannons)
function getEnemyAttackZones(piece) {
    const attackZones = [];

    switch (piece.type.type) {
        case 'CANNON':
            // Cannon can move to empty spaces without jumping, and attack by jumping over exactly one piece

            // Horizontal attacks/moves (right)
            let jumpedHorizontalRight = false;
            for (let x = piece.x + 1; x < BOARD_WIDTH; x++) {
                if (!gameBoard[piece.y][x]) {
                    // Empty space
                    if (!jumpedHorizontalRight) {
                        // Can move to empty space without jumping
                        attackZones.push({ x, y: piece.y });
                    } else {
                        // Already jumped over a piece, can't attack empty spaces
                        break;
                    }
                } else {
                    // There's a piece here
                    if (!jumpedHorizontalRight) {
                        jumpedHorizontalRight = true; // Found piece to jump over
                        // Can't attack the piece we jump over
                    } else {
                        // This is the target after jumping - can attack any piece here
                        attackZones.push({ x, y: piece.y });
                        break;
                    }
                }
            }

            // Horizontal attacks/moves (left)
            let jumpedHorizontalLeft = false;
            for (let x = piece.x - 1; x >= 0; x--) {
                if (!gameBoard[piece.y][x]) {
                    if (!jumpedHorizontalLeft) {
                        attackZones.push({ x, y: piece.y });
                    } else {
                        break;
                    }
                } else {
                    if (!jumpedHorizontalLeft) {
                        jumpedHorizontalLeft = true;
                    } else {
                        attackZones.push({ x, y: piece.y });
                        break;
                    }
                }
            }

            // Vertical attacks/moves (down)
            let jumpedVerticalDown = false;
            for (let y = piece.y + 1; y < BOARD_HEIGHT; y++) {
                if (!gameBoard[y][piece.x]) {
                    if (!jumpedVerticalDown) {
                        attackZones.push({ x: piece.x, y });
                    } else {
                        break;
                    }
                } else {
                    if (!jumpedVerticalDown) {
                        jumpedVerticalDown = true;
                    } else {
                        attackZones.push({ x: piece.x, y });
                        break;
                    }
                }
            }

            // Vertical attacks/moves (up)
            let jumpedVerticalUp = false;
            for (let y = piece.y - 1; y >= 0; y--) {
                if (!gameBoard[y][piece.x]) {
                    if (!jumpedVerticalUp) {
                        attackZones.push({ x: piece.x, y });
                    } else {
                        break;
                    }
                } else {
                    if (!jumpedVerticalUp) {
                        jumpedVerticalUp = true;
                    } else {
                        attackZones.push({ x: piece.x, y });
                        break;
                    }
                }
            }
            break;

        default:
            // For all other pieces, attack zones are the same as valid moves
            return getValidMoves(piece);
    }

    return attackZones;
}

// Get safe moves for the player (moves that won't put player in danger)
function getSafeMoves(playerMoves) {
    const safeMoves = [];

    for (const move of playerMoves) {
        // Simulate the move
        const originalPiece = gameBoard[move.y][move.x];
        const originalPlayerPos = { x: playerPiece.x, y: playerPiece.y };

        // Temporarily move the player
        gameBoard[playerPiece.y][playerPiece.x] = null;
        gameBoard[move.y][move.x] = playerPiece;
        playerPiece.x = move.x;
        playerPiece.y = move.y;

        // Check if any enemy can attack this position
        let isSafe = true;
        for (const enemyPiece of enemyPieces) {
            if (enemyPiece === originalPiece) continue; // Skip captured piece

            let canAttack = false;
            if (enemyPiece.type.type === 'CANNON') {
                // Use specialized cannon attack detection
                canAttack = canCannonAttackPosition(enemyPiece, move.x, move.y);
            } else {
                // Use general attack zone detection for other pieces
                const enemyAttacks = getEnemyAttackZones(enemyPiece);
                canAttack = enemyAttacks.some(attack => attack.x === move.x && attack.y === move.y);
            }

            if (canAttack) {
                isSafe = false;
                break;
            }
        }

        // Restore original state
        gameBoard[move.y][move.x] = originalPiece;
        gameBoard[originalPlayerPos.y][originalPlayerPos.x] = playerPiece;
        playerPiece.x = originalPlayerPos.x;
        playerPiece.y = originalPlayerPos.y;

        if (isSafe) {
            safeMoves.push(move);
        }
    }

    return safeMoves;
}

// Get best moves (captures with high value and safe)
function getBestMoves(playerMoves, safeMoves) {
    const bestMoves = [];

    // Find capture moves
    const captureMoves = playerMoves.filter(move => {
        const targetPiece = gameBoard[move.y][move.x];
        return targetPiece && !targetPiece.isPlayer;
    });

    if (captureMoves.length > 0) {
        // Sort captures by value (descending)
        captureMoves.sort((a, b) => {
            const pieceA = gameBoard[a.y][a.x];
            const pieceB = gameBoard[b.y][b.x];
            return pieceB.type.value - pieceA.type.value;
        });

        // Prefer safe captures, but also show high-value risky captures
        const safeCaptures = captureMoves.filter(move =>
            safeMoves.some(safe => safe.x === move.x && safe.y === move.y)
        );

        if (safeCaptures.length > 0) {
            bestMoves.push(...safeCaptures.slice(0, 3)); // Top 3 safe captures
        } else {
            // If no safe captures, show the highest value capture as a risky option
            bestMoves.push(captureMoves[0]);
        }
    } else {
        // No captures available, suggest safe moves that improve position
        const centerMoves = safeMoves.filter(move => {
            const centerX = Math.floor(BOARD_WIDTH / 2);
            const centerY = Math.floor(BOARD_HEIGHT / 2);
            const distanceToCenter = Math.abs(move.x - centerX) + Math.abs(move.y - centerY);
            return distanceToCenter <= 2;
        });

        if (centerMoves.length > 0) {
            bestMoves.push(...centerMoves.slice(0, 2));
        } else if (safeMoves.length > 0) {
            bestMoves.push(safeMoves[0]);
        }
    }

    return bestMoves;
}

// Initialize the game
function initGame() {
    // Reset game state
    gameBoard = [];
    enemyPieces = [];
    score = 0;
    round = 1;
    gameOver = false;
    
    // Update UI
    scoreElement.textContent = score;
    totalScoreElement.textContent = totalScore;
    highScoreElement.textContent = highScore;
    roundElement.textContent = round;
    
    // Create empty game board
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        const row = [];
        for (let x = 0; x < BOARD_WIDTH; x++) {
            row.push(null);
        }
        gameBoard.push(row);
    }
    
    // Place player's rook
    const playerX = Math.floor(BOARD_WIDTH / 2);
    const playerY = BOARD_HEIGHT - 1;
    playerPiece = {
        type: PIECE_TYPES.ROOK,
        x: playerX,
        y: playerY,
        isPlayer: true
    };
    gameBoard[playerY][playerX] = playerPiece;
    
    // Place enemy pawns
    const enemyPositions = [
        { x: 1, y: 1 },
        { x: 3, y: 1 },
        { x: 5, y: 1 }
    ];
    
    enemyPositions.forEach(pos => {
        const enemyPiece = {
            type: { ...PIECE_TYPES.PAWN, value: getUpgradedValue('PAWN') },
            x: pos.x,
            y: pos.y,
            isPlayer: false
        };
        gameBoard[pos.y][pos.x] = enemyPiece;
        enemyPieces.push(enemyPiece);
    });
    
    // Render the board
    renderBoard(true);
}

// Render the game board
function renderBoard(fullRender = false) {
    if (fullRender || gameBoardElement.children.length === 0) {
        // Full render - create all cells
        gameBoardElement.innerHTML = '';
        
        for (let y = 0; y < BOARD_HEIGHT; y++) {
            for (let x = 0; x < BOARD_WIDTH; x++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.dataset.x = x;
                cell.dataset.y = y;
                cell.addEventListener('click', () => handleCellClick(x, y));
                gameBoardElement.appendChild(cell);
            }
        }
    }
    
    // Always get valid moves for the player piece
    const playerMoves = playerPiece ? getValidMoves(playerPiece) : [];

    // Get assist mode information
    let enemyAttackZones = [];
    let safeMoves = [];
    let bestMoves = [];

    if (assistMode && playerPiece) {
        // Get all enemy attack zones
        enemyPieces.forEach(enemyPiece => {
            const attacks = getEnemyAttackZones(enemyPiece);
            enemyAttackZones = enemyAttackZones.concat(attacks);
        });

        // Get safe moves and best moves
        safeMoves = getSafeMoves(playerMoves);
        bestMoves = getBestMoves(playerMoves, safeMoves);

        // Also mark enemy pieces that are dangerous to capture
        // (pieces that if captured, would put the player in danger)
        enemyPieces.forEach(enemyPiece => {
            // Check if player can capture this piece
            if (playerMoves.some(move => move.x === enemyPiece.x && move.y === enemyPiece.y)) {
                // Check if capturing this piece would put player in danger
                // Simulate the capture by temporarily removing the piece
                const originalPiece = gameBoard[enemyPiece.y][enemyPiece.x];
                gameBoard[enemyPiece.y][enemyPiece.x] = null;

                // Check if any other enemy piece can attack the position where player would be
                const wouldBeInDanger = enemyPieces.some(otherEnemyPiece => {
                    if (otherEnemyPiece === enemyPiece) return false; // Skip the piece being captured
                    const otherAttacks = getEnemyAttackZones(otherEnemyPiece);
                    return otherAttacks.some(attack => attack.x === enemyPiece.x && attack.y === enemyPiece.y);
                });

                // Restore the piece
                gameBoard[enemyPiece.y][enemyPiece.x] = originalPiece;

                // If capturing would be dangerous, mark this enemy piece position as dangerous
                if (wouldBeInDanger) {
                    enemyAttackZones.push({ x: enemyPiece.x, y: enemyPiece.y });
                }
            }
        });

        // Update assist panel statistics
        updateAssistStats(playerMoves, safeMoves, bestMoves, enemyAttackZones);
    }
    
    // Update all cells with current state
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        for (let x = 0; x < BOARD_WIDTH; x++) {
            const cellIndex = y * BOARD_WIDTH + x;
            const cell = gameBoardElement.children[cellIndex];
            
            // Clear previous classes and content
            cell.className = 'cell';
            cell.innerHTML = '';
            
            // Add piece if exists
            const piece = gameBoard[y][x];
            if (piece) {
                const pieceElement = document.createElement('div');
                pieceElement.className = `piece ${piece.isPlayer ? 'player' : 'enemy'} piece-${piece.type.type.toLowerCase()}`;
                pieceElement.textContent = piece.type.name;
                pieceElement.dataset.pieceId = `${piece.type.type}-${x}-${y}`;
                cell.appendChild(pieceElement);
                
                // Always highlight the player piece
                if (piece.isPlayer) {
                    pieceElement.classList.add('selected');
                } else if (assistMode) {
                    // Add threat level indicator for enemy pieces
                    let canAttackPlayer = false;

                    if (piece.type.type === 'CANNON') {
                        // Use specialized cannon attack detection
                        canAttackPlayer = canCannonAttackPosition(piece, playerPiece.x, playerPiece.y);
                    } else {
                        // Use general attack zone detection for other pieces
                        const enemyAttacks = getEnemyAttackZones(piece);
                        canAttackPlayer = enemyAttacks.some(attack =>
                            attack.x === playerPiece.x && attack.y === playerPiece.y
                        );
                    }

                    if (canAttackPlayer) {
                        pieceElement.classList.add('threatening');
                        pieceElement.title = `威胁棋子: ${piece.type.name} - 可攻击你的位置!`;
                    } else {
                        pieceElement.title = `${piece.type.name} (${piece.type.value}分)`;
                    }
                }
            }
            
            // Always highlight valid moves for the player
            if (playerMoves.some(move => move.x === x && move.y === y)) {
                cell.classList.add('valid-move');
            }

            // Assist mode highlights
            if (assistMode) {
                // Highlight enemy attack zones (dangerous areas)
                if (enemyAttackZones.some(zone => zone.x === x && zone.y === y)) {
                    cell.classList.add('enemy-attack-zone');
                }

                // Highlight safe moves (green border)
                if (safeMoves.some(move => move.x === x && move.y === y)) {
                    cell.classList.add('safe-move');
                }

                // Highlight best moves (gold border)
                if (bestMoves.some(move => move.x === x && move.y === y)) {
                    cell.classList.add('best-move');

                    // Add tooltip for best moves
                    const targetPiece = gameBoard[y][x];
                    if (targetPiece && !targetPiece.isPlayer) {
                        cell.title = `推荐捕获: ${targetPiece.type.name} (${targetPiece.type.value}分)`;
                    } else {
                        cell.title = '推荐移动位置';
                    }
                }

                // Add tooltips for safe moves
                if (safeMoves.some(move => move.x === x && move.y === y) &&
                    !bestMoves.some(move => move.x === x && move.y === y)) {
                    const targetPiece = gameBoard[y][x];
                    if (targetPiece && !targetPiece.isPlayer) {
                        cell.title = `安全捕获: ${targetPiece.type.name} (${targetPiece.type.value}分)`;
                    } else {
                        cell.title = '安全移动位置';
                    }
                }

                // Add tooltips for dangerous zones
                if (enemyAttackZones.some(zone => zone.x === x && zone.y === y)) {
                    if (!cell.title) {
                        cell.title = '危险区域 - 敌方可攻击此位置';
                    }
                }
            }
            
            // Highlight last move
            if (lastMoveFrom && lastMoveFrom.x === x && lastMoveFrom.y === y) {
                cell.classList.add('last-move');
            }
            if (lastMoveTo && lastMoveTo.x === x && lastMoveTo.y === y) {
                cell.classList.add('last-move');
            }
        }
    }
}

// Handle cell click
function handleCellClick(x, y) {
    if (gameOver) return;

    const clickedPiece = gameBoard[y][x];

    // If clicked on player's own piece, do nothing (since we auto-select)
    if (clickedPiece && clickedPiece.isPlayer) {
        return;
    }

    // Always show valid moves for the player piece
    const playerMoves = getValidMoves(playerPiece);

    // If clicked position is a valid move, move there
    if (playerMoves.some(move => move.x === x && move.y === y)) {
        // Show move confirmation in assist mode
        if (assistMode) {
            showMovePreview(x, y);
        } else {
            movePlayerPiece(x, y);
        }
        return;
    }

    // If clicked on an invalid position, do nothing (could show a message)
}

// Show move preview in assist mode
function showMovePreview(x, y) {
    const targetPiece = gameBoard[y][x];
    let message = `移动到 (${x+1}, ${y+1})`;

    if (targetPiece && !targetPiece.isPlayer) {
        message += `\n捕获: ${targetPiece.type.name} (+${targetPiece.type.value}分)`;
    }

    // Check if move is safe
    const playerMoves = getValidMoves(playerPiece);
    const safeMoves = getSafeMoves(playerMoves);
    const isSafe = safeMoves.some(move => move.x === x && move.y === y);

    if (!isSafe) {
        message += '\n⚠️ 警告: 此移动可能有危险!';
    }

    // Show confirmation dialog
    if (confirm(message + '\n\n确认移动吗?')) {
        movePlayerPiece(x, y);
    }
}

// Get valid moves for a piece
function getValidMoves(piece) {
    const moves = [];
    
    switch (piece.type.type) {
        case 'ROOK':
            // Rook moves horizontally and vertically
            // Horizontal moves (right)
            for (let x = piece.x + 1; x < BOARD_WIDTH; x++) {
                moves.push({ x, y: piece.y });
                if (gameBoard[piece.y][x]) break; // Stop at obstacle
            }
            // Horizontal moves (left)
            for (let x = piece.x - 1; x >= 0; x--) {
                moves.push({ x, y: piece.y });
                if (gameBoard[piece.y][x]) break; // Stop at obstacle
            }
            // Vertical moves (down)
            for (let y = piece.y + 1; y < BOARD_HEIGHT; y++) {
                moves.push({ x: piece.x, y });
                if (gameBoard[y][piece.x]) break; // Stop at obstacle
            }
            // Vertical moves (up)
            for (let y = piece.y - 1; y >= 0; y--) {
                moves.push({ x: piece.x, y });
                if (gameBoard[y][piece.x]) break; // Stop at obstacle
            }
            break;
            
        case 'CANNON':
            // Cannon moves like rook but jumps over one piece to capture
            let jumpedHorizontalRight = false;
            // Horizontal moves (right)
            for (let x = piece.x + 1; x < BOARD_WIDTH; x++) {
                if (!gameBoard[piece.y][x]) {
                    if (!jumpedHorizontalRight) {
                        moves.push({ x, y: piece.y });
                    }
                } else {
                    if (!jumpedHorizontalRight) {
                        jumpedHorizontalRight = true;
                    } else {
                        if (gameBoard[piece.y][x].isPlayer !== piece.isPlayer) {
                            moves.push({ x, y: piece.y });
                        }
                        break;
                    }
                }
            }
            
            let jumpedHorizontalLeft = false;
            // Horizontal moves (left)
            for (let x = piece.x - 1; x >= 0; x--) {
                if (!gameBoard[piece.y][x]) {
                    if (!jumpedHorizontalLeft) {
                        moves.push({ x, y: piece.y });
                    }
                } else {
                    if (!jumpedHorizontalLeft) {
                        jumpedHorizontalLeft = true;
                    } else {
                        if (gameBoard[piece.y][x].isPlayer !== piece.isPlayer) {
                            moves.push({ x, y: piece.y });
                        }
                        break;
                    }
                }
            }
            
            let jumpedVerticalDown = false;
            // Vertical moves (down)
            for (let y = piece.y + 1; y < BOARD_HEIGHT; y++) {
                if (!gameBoard[y][piece.x]) {
                    if (!jumpedVerticalDown) {
                        moves.push({ x: piece.x, y });
                    }
                } else {
                    if (!jumpedVerticalDown) {
                        jumpedVerticalDown = true;
                    } else {
                        if (gameBoard[y][piece.x].isPlayer !== piece.isPlayer) {
                            moves.push({ x: piece.x, y });
                        }
                        break;
                    }
                }
            }
            
            let jumpedVerticalUp = false;
            // Vertical moves (up)
            for (let y = piece.y - 1; y >= 0; y--) {
                if (!gameBoard[y][piece.x]) {
                    if (!jumpedVerticalUp) {
                        moves.push({ x: piece.x, y });
                    }
                } else {
                    if (!jumpedVerticalUp) {
                        jumpedVerticalUp = true;
                    } else {
                        if (gameBoard[y][piece.x].isPlayer !== piece.isPlayer) {
                            moves.push({ x: piece.x, y });
                        }
                        break;
                    }
                }
            }
            break;
            
        case 'KNIGHT':
            // Knight moves in L-shape
            const knightMoves = [
                { x: piece.x + 1, y: piece.y - 2 },
                { x: piece.x + 2, y: piece.y - 1 },
                { x: piece.x + 2, y: piece.y + 1 },
                { x: piece.x + 1, y: piece.y + 2 },
                { x: piece.x - 1, y: piece.y + 2 },
                { x: piece.x - 2, y: piece.y + 1 },
                { x: piece.x - 2, y: piece.y - 1 },
                { x: piece.x - 1, y: piece.y - 2 }
            ];
            
            // Check for obstacles in the way (Chinese chess knight can be blocked)
            for (const move of knightMoves) {
                if (move.x >= 0 && move.x < BOARD_WIDTH && move.y >= 0 && move.y < BOARD_HEIGHT) {
                    // Calculate the position of the potential blocking piece
                    let blockingX = piece.x;
                    let blockingY = piece.y;
                    
                    // Determine which direction to check for blocking
                    if (Math.abs(move.x - piece.x) === 2) {
                        blockingX = piece.x + (move.x > piece.x ? 1 : -1);
                    } else {
                        blockingY = piece.y + (move.y > piece.y ? 1 : -1);
                    }
                    
                    // If the path is not blocked
                    if (!gameBoard[blockingY][blockingX]) {
                        // If destination is empty or has enemy piece
                        if (!gameBoard[move.y][move.x] || gameBoard[move.y][move.x].isPlayer !== piece.isPlayer) {
                            moves.push(move);
                        }
                    }
                }
            }
            break;
            
        case 'ELEPHANT':
            // Elephant moves diagonally 2 steps
            const elephantMoves = [
                { x: piece.x + 2, y: piece.y + 2 },
                { x: piece.x + 2, y: piece.y - 2 },
                { x: piece.x - 2, y: piece.y + 2 },
                { x: piece.x - 2, y: piece.y - 2 }
            ];
            
            for (const move of elephantMoves) {
                if (move.x >= 0 && move.x < BOARD_WIDTH && move.y >= 0 && move.y < BOARD_HEIGHT) {
                    // Check for blocking piece at the diagonal center
                    const blockingX = piece.x + (move.x > piece.x ? 1 : -1);
                    const blockingY = piece.y + (move.y > piece.y ? 1 : -1);
                    
                    if (!gameBoard[blockingY][blockingX]) {
                        // If destination is empty or has enemy piece
                        if (!gameBoard[move.y][move.x] || gameBoard[move.y][move.x].isPlayer !== piece.isPlayer) {
                            moves.push(move);
                        }
                    }
                }
            }
            break;
            
        case 'ADVISOR':
            // Advisor moves diagonally 1 step
            const advisorMoves = [
                { x: piece.x + 1, y: piece.y + 1 },
                { x: piece.x + 1, y: piece.y - 1 },
                { x: piece.x - 1, y: piece.y + 1 },
                { x: piece.x - 1, y: piece.y - 1 }
            ];
            
            for (const move of advisorMoves) {
                if (move.x >= 0 && move.x < BOARD_WIDTH && move.y >= 0 && move.y < BOARD_HEIGHT) {
                    // If destination is empty or has enemy piece
                    if (!gameBoard[move.y][move.x] || gameBoard[move.y][move.x].isPlayer !== piece.isPlayer) {
                        moves.push(move);
                    }
                }
            }
            break;
            
        case 'GENERAL':
            // General moves one step horizontally or vertically
            const generalMoves = [
                { x: piece.x + 1, y: piece.y },
                { x: piece.x - 1, y: piece.y },
                { x: piece.x, y: piece.y + 1 },
                { x: piece.x, y: piece.y - 1 }
            ];
            
            for (const move of generalMoves) {
                if (move.x >= 0 && move.x < BOARD_WIDTH && move.y >= 0 && move.y < BOARD_HEIGHT) {
                    // If destination is empty or has enemy piece
                    if (!gameBoard[move.y][move.x] || gameBoard[move.y][move.x].isPlayer !== piece.isPlayer) {
                        moves.push(move);
                    }
                }
            }
            break;
            
        case 'PAWN':
            // Pawn moves differently based on whose piece it is
            if (piece.isPlayer) {
                // Player's pawn can move forward
                if (piece.y > 0) {
                    moves.push({ x: piece.x, y: piece.y - 1 });
                }
            } else {
                // Enemy pawn can move in all four directions
                if (piece.y > 0) {
                    moves.push({ x: piece.x, y: piece.y - 1 }); // up
                }
                if (piece.y < BOARD_HEIGHT - 1) {
                    moves.push({ x: piece.x, y: piece.y + 1 }); // down
                }
                if (piece.x > 0) {
                    moves.push({ x: piece.x - 1, y: piece.y }); // left
                }
                if (piece.x < BOARD_WIDTH - 1) {
                    moves.push({ x: piece.x + 1, y: piece.y }); // right
                }
            }
            break;
    }
    
    // Filter out moves that would capture player's own pieces
    return moves.filter(move => {
        const targetPiece = gameBoard[move.y][move.x];
        return !targetPiece || targetPiece.isPlayer !== piece.isPlayer;
    });
}

// Move player's piece
function movePlayerPiece(x, y) {
    const fromX = playerPiece.x;
    const fromY = playerPiece.y;
    const targetPiece = gameBoard[y][x];
    
    // Update last move tracking
    lastMoveFrom = { x: fromX, y: fromY };
    lastMoveTo = { x, y };
    
    // If there's a piece at the target location, add its value to score
    if (targetPiece) {
        // Create a visual effect for capturing
        const captureEffect = document.createElement('div');
        captureEffect.className = 'capture-effect';
        captureEffect.textContent = `+${targetPiece.type.value}`;
        document.body.appendChild(captureEffect);
        
        // Position the effect near the captured piece
        const cellRect = document.querySelector(`.cell[data-x="${x}"][data-y="${y}"]`).getBoundingClientRect();
        captureEffect.style.left = `${cellRect.left + cellRect.width/2}px`;
        captureEffect.style.top = `${cellRect.top}px`;
        
        // Animate and remove
        setTimeout(() => {
            captureEffect.style.opacity = '0';
            captureEffect.style.transform = 'translateY(-30px)';
            setTimeout(() => document.body.removeChild(captureEffect), 1000);
        }, 10);
        
        score += targetPiece.type.value;
        scoreElement.textContent = score;
        
        // Remove the captured piece from enemyPieces array
        enemyPieces = enemyPieces.filter(p => p !== targetPiece);
    }
    
    // Update the board
    gameBoard[playerPiece.y][playerPiece.x] = null;
    gameBoard[y][x] = playerPiece;
    
    // Update player piece position
    playerPiece.x = x;
    playerPiece.y = y;
    
    // Re-render the board to show new valid moves
    renderBoard();
    
    // Check if game is over after player's move
    if (!checkGameOver()) {
        // Enemy's turn
        setTimeout(enemyTurn, 800);
    }
}

// Enemy's turn
function enemyTurn() {
    // Check if any enemy piece can capture the player
    for (const enemyPiece of enemyPieces) {
        const enemyMoves = getValidMoves(enemyPiece);
        
        // Check if any move can capture the player
        const captureMove = enemyMoves.find(move => 
            move.x === playerPiece.x && move.y === playerPiece.y
        );
        
        if (captureMove) {
            // Capture the player
            gameBoard[enemyPiece.y][enemyPiece.x] = null;
            gameBoard[playerPiece.y][playerPiece.x] = enemyPiece;
            enemyPiece.x = playerPiece.x;
            enemyPiece.y = playerPiece.y;
            
            // Game over - pass the defeating piece
            endGame("An enemy piece captured your piece!", enemyPiece);
            return;
        }
    }
    
    // Check if any enemy piece is threatened by the player
    const threatenedPieces = enemyPieces.filter(enemyPiece => {
        const playerMoves = getValidMoves(playerPiece);
        return playerMoves.some(move => 
            move.x === enemyPiece.x && move.y === enemyPiece.y
        );
    });
    
    if (threatenedPieces.length > 0) {
        // Move a random threatened piece
        const pieceToMove = threatenedPieces[Math.floor(Math.random() * threatenedPieces.length)];
        const validMoves = getValidMoves(pieceToMove);
        
        if (validMoves.length > 0) {
            const randomMove = validMoves[Math.floor(Math.random() * validMoves.length)];
            
            // Move the piece
            gameBoard[pieceToMove.y][pieceToMove.x] = null;
            
            // If there's a piece at the target location (should not be player's piece due to getValidMoves filtering)
            if (gameBoard[randomMove.y][randomMove.x]) {
                // Remove the captured piece from enemyPieces array
                enemyPieces = enemyPieces.filter(p => 
                    p !== gameBoard[randomMove.y][randomMove.x]
                );
            }
            
            gameBoard[randomMove.y][randomMove.x] = pieceToMove;
            pieceToMove.x = randomMove.x;
            pieceToMove.y = randomMove.y;
            
            // End enemy turn
            endEnemyTurn();
            return;
        }
    }
    
    // If no threatened pieces or no valid moves for threatened pieces,
    // move a random piece
    if (enemyPieces.length > 0) {
        const randomIndex = Math.floor(Math.random() * enemyPieces.length);
        const pieceToMove = enemyPieces[randomIndex];
        const validMoves = getValidMoves(pieceToMove);
        
        if (validMoves.length > 0) {
            const randomMove = validMoves[Math.floor(Math.random() * validMoves.length)];
            
            // Move the piece
            gameBoard[pieceToMove.y][pieceToMove.x] = null;
            
            // If there's a piece at the target location (should not be player's piece due to getValidMoves filtering)
            if (gameBoard[randomMove.y][randomMove.x]) {
                // Remove the captured piece from enemyPieces array
                enemyPieces = enemyPieces.filter(p => 
                    p !== gameBoard[randomMove.y][randomMove.x]
                );
            }
            
            gameBoard[randomMove.y][randomMove.x] = pieceToMove;
            pieceToMove.x = randomMove.x;
            pieceToMove.y = randomMove.y;
        }
    }
    
    // End enemy turn
    endEnemyTurn();
}

// End enemy turn and start a new round
function endEnemyTurn() {
    round++;
    roundElement.textContent = round;
    
    // Every 4 rounds, add 3 new pieces
    if (round % 4 === 0) {
        addNewPieces(3);
    }
    
    // Render the updated board (full render for enemy moves and new pieces)
    renderBoard();
    
    // Check if game is over after enemy's move
    checkGameOver();
}

// Add new enemy pieces to the board
function addNewPieces(count) {
    const pieceTypes = Object.values(PIECE_TYPES);
    const emptyPositions = [];
    
    // Find all empty positions
    for (let y = 0; y < BOARD_HEIGHT - 2; y++) { // Avoid bottom two rows
        for (let x = 0; x < BOARD_WIDTH; x++) {
            if (!gameBoard[y][x]) {
                emptyPositions.push({ x, y });
            }
        }
    }
    
    // Shuffle empty positions
    for (let i = emptyPositions.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [emptyPositions[i], emptyPositions[j]] = [emptyPositions[j], emptyPositions[i]];
    }
    
    // Add new pieces
    const addedPieces = [];
    for (let i = 0; i < Math.min(count, emptyPositions.length); i++) {
        const pos = emptyPositions[i];
        const randomType = pieceTypes[Math.floor(Math.random() * pieceTypes.length)];
        
        // Create piece with upgraded value
        const upgradedType = { ...randomType };
        upgradedType.value = getUpgradedValue(randomType.type);
        
        const newPiece = {
            type: upgradedType,
            x: pos.x,
            y: pos.y,
            isPlayer: false
        };
        
        gameBoard[pos.y][pos.x] = newPiece;
        enemyPieces.push(newPiece);
        addedPieces.push(newPiece);
    }
    
    // Show notification about new pieces
    if (addedPieces.length > 0) {
        showNotification(`${addedPieces.length} new enemy pieces appeared!`);
    }
}

// Show a notification message
function showNotification(message) {
    // Create notification element if it doesn't exist
    let notification = document.getElementById('game-notification');
    if (!notification) {
        notification = document.createElement('div');
        notification.id = 'game-notification';
        notification.className = 'game-notification hidden';
        document.body.appendChild(notification);
    }
    
    // Set message and show
    notification.textContent = message;
    notification.classList.remove('hidden');
    
    // Hide after a delay
    setTimeout(() => {
        notification.classList.add('hidden');
    }, 3000);
}

// Check if the game is over
function checkGameOver() {
    // Check if player has no valid moves
    const playerMoves = getValidMoves(playerPiece);
    if (playerMoves.length === 0) {
        endGame("You have no valid moves left!");
        return true;
    }
    
    return false;
}

// End the game
function endGame(reason, defeatingPiece = null) {
    gameOver = true;
    
    // Add current score to total score
    totalScore += score;
    localStorage.setItem('chessTotalScore', totalScore);
    totalScoreElement.textContent = totalScore;
    
    // Update high score if needed
    if (score > highScore) {
        highScore = score;
        localStorage.setItem('chessGameHighScore', highScore);
        highScoreElement.textContent = highScore;
    }
    
    // Show game over screen
    finalScoreElement.textContent = `Your score: ${score}`;
    
    // Display the defeating piece if available
    const defeatInfoElement = document.getElementById('defeat-info');
    const defeatingPieceElement = document.getElementById('defeating-piece');
    
    if (defeatingPiece) {
        defeatingPieceElement.innerHTML = '';
        const pieceElement = document.createElement('div');
        pieceElement.className = `piece enemy defeated piece-${defeatingPiece.type.type.toLowerCase()}`;
        pieceElement.textContent = defeatingPiece.type.name;
        defeatingPieceElement.appendChild(pieceElement);
        defeatInfoElement.style.display = 'block';
    } else {
        defeatInfoElement.style.display = 'none';
    }
    
    // Set message based on score
    let message = "";
    if (score >= 3000) {
        message = "弓马骑射洒热血，突破重围显英豪!";
    } else if (score >= 2000) {
        message = "一人一枪一匹马，疆场尽驰骋!";
    } else if (score >= 1000) {
        message = "匹马单枪出重围，英风锐气敌胆寒";
    } else if (score >= 500) {
        message = "翩若惊鸿，婉若游龙";
    } else {
        message = "魂归在何处，仰天长问三两声";
    }
    
    messageElement.textContent = message;
    gameOverElement.classList.remove('hidden');
}

// Shop functions
function openShop() {
    console.log('Opening shop...');
    renderShop();
    if (shopElement) {
        shopElement.classList.remove('hidden');
        shopElement.style.display = 'flex';
        console.log('Shop opened successfully');
    } else {
        console.error('Shop element not found');
    }
}

function closeShop() {
    console.log('Closing shop...');
    if (shopElement) {
        shopElement.classList.add('hidden');
        shopElement.style.display = 'none';
        console.log('Shop closed successfully');
    }
}

function renderShop() {
    console.log('renderShop called');
    const shopContent = document.getElementById('shop-content');
    if (!shopContent) {
        console.error('shop-content element not found');
        return;
    }
    console.log('Clearing shop content');
    shopContent.innerHTML = '';
    
    // Shop header
    const header = document.createElement('div');
    header.className = 'shop-header';
    header.innerHTML = `
        <h2>升级商店</h2>
        <p>总分数: ${totalScore}</p>
        <button id="close-shop" class="close-button">&times;</button>
    `;
    shopContent.appendChild(header);
    
    // Shop items
    const itemsContainer = document.createElement('div');
    itemsContainer.className = 'shop-items';
    
    Object.keys(PIECE_TYPES).forEach(pieceType => {
        const currentValue = getUpgradedValue(pieceType);
        const upgradeCost = getUpgradeCost(pieceType);
        const upgradeLevel = pieceUpgrades[pieceType];
        
        const item = document.createElement('div');
        item.className = 'shop-item';
        item.innerHTML = `
            <div class="piece-info">
                <div class="legend-piece ${pieceType.toLowerCase()}">${PIECE_TYPES[pieceType].name}</div>
                <div class="piece-details">
                    <div>当前分数: ${currentValue}</div>
                    <div>升级等级: ${upgradeLevel}</div>
                    <div>升级费用: ${upgradeCost}</div>
                </div>
            </div>
            <button class="upgrade-btn" data-piece="${pieceType}" ${totalScore < upgradeCost ? 'disabled' : ''}>
                升级 (+50%)
            </button>
        `;
        itemsContainer.appendChild(item);
    });
    
    shopContent.appendChild(itemsContainer);
    
    // Add event listeners
    document.getElementById('close-shop').addEventListener('click', closeShop);
    
    document.querySelectorAll('.upgrade-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const pieceType = e.target.dataset.piece;
            upgradePiece(pieceType);
        });
    });
}

function upgradePiece(pieceType) {
    const cost = getUpgradeCost(pieceType);
    
    if (totalScore >= cost) {
        totalScore -= cost;
        pieceUpgrades[pieceType]++;
        
        // Update existing pieces on the board immediately
        updateExistingPieces(pieceType);
        
        // Save to localStorage
        localStorage.setItem('chessTotalScore', totalScore);
        localStorage.setItem('chessUpgrades', JSON.stringify(pieceUpgrades));
        
        // Update UI
        totalScoreElement.textContent = totalScore;
        renderShop();
        
        const newValue = getUpgradedValue(pieceType);
        showNotification(`${PIECE_TYPES[pieceType].name} 升级成功! 新分数: ${newValue}`);
    }
}

// Event listeners
startButton.addEventListener('click', () => {
    initGame();
    gameOverElement.classList.add('hidden');
});

restartButton.addEventListener('click', () => {
    initGame();
    gameOverElement.classList.add('hidden');
});

shopButton.addEventListener('click', () => {
    console.log('Shop button clicked');
    openShop();
});

assistButton.addEventListener('click', () => {
    toggleAssistMode();
});

// Also add click event to shop background to close it
shopElement.addEventListener('click', (e) => {
    if (e.target === shopElement) {
        closeShop();
    }
});

// Close game over popup without restarting
const closeGameOverButton = document.getElementById('close-game-over');
if (closeGameOverButton) {
    closeGameOverButton.addEventListener('click', () => {
        gameOverElement.classList.add('hidden');
    });
}

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.key === 'h' || e.key === 'H') {
        toggleAssistMode();
    }
});

// Initialize the game when the page loads
window.addEventListener('DOMContentLoaded', () => {
    totalScoreElement.textContent = totalScore;
    initGame();
});
